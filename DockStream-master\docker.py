#!/usr/bin/env python
#  coding=utf-8

import os
import sys
import warnings
import argparse

# Commented out commercial tools - only AutoDock Vina is supported in this version
# from dockstream.core.rDock.rDock_docker import rDock
# from dockstream.core.OpenEye.OpenEye_docker import OpenEye
# from dockstream.core.Schrodinger.Glide_docker import Glide
from dockstream.core.AutodockVina.AutodockVina_docker import AutodockVina
# from dockstream.core.OpenEyeHybrid.OpenEyeHybrid_docker import OpenEyeHybrid

from dockstream.utils.entry_point_functions.header import initialize_logging, set_environment
# from dockstream.utils.entry_point_functions.embedding import embed_ligands  # Commented out - has OpenEye dependencies
from dockstream.utils.entry_point_functions.write_out import handle_poses_writeout, handle_score_printing, \
                                                         handle_scores_writeout

from dockstream.utils.enums.docking_enum import DockingConfigurationEnum
from dockstream.utils.enums.ligand_preparation_enum import LigandPreparationEnum
from dockstream.utils.enums.logging_enums import LoggingConfigEnum

from dockstream.utils.files_paths import attach_root_path
from dockstream.utils.argparse_bool_extension import str2bool
from dockstream.utils.dockstream_exceptions import *


if __name__ == "__main__":
    print("Hello World");
    # enums
    _LE = LoggingConfigEnum()
    _LP = LigandPreparationEnum()
    _DE = DockingConfigurationEnum()
    
    # Only AutoDock Vina is supported in this version (commercial tools are commented out)
    ALLOWED_BACKENDS = ["AutodockVina"]

    # get the input parameters and parse them
    parser = argparse.ArgumentParser(description="Implements entry point for the docking using one or multiple backends.")
    parser.add_argument("-conf", type=str, default=None, help="A path to an docking configuration file (JSON dictionary) that is to be executed.", required=True)
    parser.add_argument("-validation", type=str2bool, default=True, help="If set to False, this flag will prohibit a JSON Schema validation.")
    parser.add_argument("-silent", type=str2bool, default=False, help="If set, the program will silently execute without printing status updates.")
    parser.add_argument("-smiles", default=None, help="Use this flag to hand over the input SMILES over the command-line, separated by ';'.", type=str, required=False)
    parser.add_argument("-print_scores", action="store_true", help="Set this flag to activate linewise print-outs of the scores to the shell.")
    parser.add_argument("-print_all", action="store_true", help="Set this flag (together with \"-print_scores\") to print out the scores for all conformers, not just the best one.")
    parser.add_argument("-debug", action="store_true", help="Set this flag to activate the inbuilt debug logging mode (this will overwrite parameter \"-log_conf\", if set).")
    parser.add_argument("-log_conf", type=str, default=None, help="Set absolute path to a logger configuration other than the default stored at \"config/logging/default.json\".")
    parser.add_argument("-output_prefix", type=str, default=None, help="If specified, this prefix will be added to all output file names.")
    parser.add_argument("-input_csv", type=str, default=None, help="If set (a path to a CSV file), this will overwrite any input file specification in the configuration.")
    parser.add_argument("-input_csv_smiles_column", type=str, default=None, help="If \"-input_csv\" is set, you need to specify the column name with the smiles as well.")
    parser.add_argument("-input_csv_names_column", type=str, default=None, help="Optional name of the name column, if \"-input_csv\" is specified.")
    args, args_unk = parser.parse_known_args()

    if args.conf is None or not os.path.isfile(args.conf):
        raise Exception("Parameter \"-conf\" must be a relative or absolute path to a configuration JSON file.")
    if args.print_scores is False and args.print_all:
        raise Exception("Flag \"-print_scores\" must be activated in order to use \"-print_all\", see help message.")

    # set the logging configuration according to parameters
    if args.log_conf is None:
        args.log_conf = attach_root_path(_LE.PATH_CONFIG_DEFAULT)
    if args.debug:
        args.log_conf = attach_root_path(_LE.PATH_CONFIG_DEBUG)

    # initialize the docking Enum and get the configuration
    try:
        config_dict = DockingContainer(conf=args.conf, validation=args.validation)
    except Exception as e:
        raise DockingRunFailed() from e

    # check if backend is supported (only AutoDock Vina is supported in this version)
    backend = config_dict[_DE.PIPELINE][_DE.BACKEND].upper()
    if backend != "AUTODOCKVINA":
        raise BackendNotAvailableException(
            f"This version only supports AutoDock Vina (commercial tools are commented out). "
            f"Please set the backend to 'AutoDockVina' in your configuration."
        )

    # initialize the logger
    logger = initialize_logging(config=config_dict, task=_DE.DOCKING, _task_enum=_DE, log_conf_path=args.log_conf)
    set_environment(config=config_dict, task=_DE.DOCKING, _task_enum=_DE, logger=logger)

    # check, if there are unknown arguments
    if len(args_unk) > 0:
        logger.log(f"Unknown arguments: {args_unk}.", _LE.WARNING)

    # Initialize the AutoDock Vina backend
    # Note: Other backends are commented out in this version
    try:
        docker = AutodockVina(**config_dict)
        
        # Add molecules if provided via command line
        if args.smiles:
            from dockstream.core.ligand.ligand import Ligand
            smiles_list = args.smiles.split(';')
            ligands = [Ligand(smi, None, None) for smi in smiles_list]
            docker.add_molecules(ligands)
        
        # Do the docking
        docker.dock()
        
        # Save results and print scores
        handle_poses_writeout(docking_run=config_dict, docker=docker, output_prefix=args.output_prefix)
        handle_scores_writeout(docking_run=config_dict, docker=docker, output_prefix=args.output_prefix)
        handle_score_printing(print_scores=args.print_scores,
                            print_all=args.print_all,
                            docker=docker,
                            logger=logger)
        
        logger.log("Docking completed successfully.", _LE.INFO)
        
    except Exception as e:
        logger.log("Docking failed.", _LE.EXCEPTION)
        logger.log(f"Exception reads: {get_exception_message(e)}.", _LE.EXCEPTION)
        raise DockingRunFailed() from e
    
    sys.exit(0)
