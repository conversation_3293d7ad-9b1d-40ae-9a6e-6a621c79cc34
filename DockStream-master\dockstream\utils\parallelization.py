"""
Utility functions for parallel processing.
"""
from typing import Optional
from pydantic import BaseModel


class Parallelization(BaseModel):
    """
    A class to handle parallelization parameters.
    """
    number_cores: Optional[int] = 1
    max_compounds_per_subjob: Optional[int] = 0
    
    class Config:
        extra = "allow"  # Allow extra fields for backward compatibility
        
    def get(self, key: str, default=None):
        """Helper method to support dictionary-style access for backward compatibility"""
        return getattr(self, key, default)
