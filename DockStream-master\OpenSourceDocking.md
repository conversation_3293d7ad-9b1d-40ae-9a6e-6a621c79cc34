# DockStream Integration in REINVENT Scoring Package

## Overview
DockStream is a flexible docking workflow that supports multiple docking backends, including both commercial and open-source options. In the context of PROTAC design, it's used to score generated PROTAC molecules based on their binding to target proteins.

## Open-Source Docking Backends

### 1. AutoDock Vina
- **Location**: [dockstream/core/AutodockVina/](cci:7://file:///d:/protac-invent/DockStream-master/dockstream/core/AutodockVina:0:0-0:0)
- **Key Files**:
  - [AutodockVina_docker.py](cci:7://file:///d:/protac-invent/DockStream-master/dockstream/core/AutodockVina/AutodockVina_docker.py:0:0-0:0): Main docking implementation
  - [AutodockVina_target_preparator.py](cci:7://file:///d:/protac-invent/DockStream-master/dockstream/core/AutodockVina/AutodockVina_target_preparator.py:0:0-0:0): Prepares target proteins
  - [AutodockVina_result_parser.py](cci:7://file:///d:/protac-invent/DockStream-master/dockstream/core/AutodockVina/AutodockVina_result_parser.py:0:0-0:0): Parses docking results

- **Workflow**:
  1. Input molecules are converted to PDBQT format
  2. Target protein is prepared with the target preparator
  3. Docking is performed using AutoDock Vina
  4. Results are parsed and converted back to SDF format

### 2. rDock
- **Location**: [dockstream/core/rDock/](cci:7://file:///d:/protac-invent/DockStream-master/dockstream/core/rDock:0:0-0:0)
- **Key Files**:
  - [rDock_docker.py](cci:7://file:///d:/protac-invent/DockStream-master/dockstream/core/rDock/rDock_docker.py:0:0-0:0): Main docking implementation
  - [rDock_target_preparator.py](cci:7://file:///d:/protac-invent/DockStream-master/dockstream/core/rDock/rDock_target_preparator.py:0:0-0:0): Prepares target proteins
  - [rDock_result_parser.py](cci:7://file:///d:/protac-invent/DockStream-master/dockstream/core/rDock/rDock_result_parser.py:0:0-0:0): Parses docking results

- **Workflow**:
  1. Input molecules are prepared for rDock
  2. Target protein is processed and parameter files are generated
  3. Docking is performed using rDock
  4. Results are processed and returned

## Scoring Components

### 1. Docking Score (dockstream)
- **Purpose**: Evaluates binding energy and pose of generated PROTACs
- **Process**:
  - Docks generated PROTACs to the target protein
  - Returns scores based on binding energy and pose quality
  - Handles format conversions (SDF ↔ PDBQT)

### 2. ROCS Similarity (docked_parallel_rocs_similarity)
- **Purpose**: Compares docked poses to reference PROTAC structures
- **Current Implementation**:
  - Uses OpenEye's ROCS for 3D shape and feature comparison
  - Proprietary software (requires OpenEye license)

## Proprietary Dependencies and Open-Source Alternatives

### 1. ROCS Replacement Options

#### a. Open3DAlign
- **Type**: Open-source
- **Features**:
  - 3D molecular alignment
  - Shape and feature-based comparison
  - Can be integrated via Python bindings

#### b. Shape-It
- **Type**: Open-source
- **Features**:
  - Rapid 3D molecular alignment
  - Shape and feature-based scoring
  - Command-line interface available

#### c. RDKit Shape Alignment
- **Type**: Open-source
- **Features**:
  - Built into RDKit
  - Shape similarity metrics (Tanimoto, Protrude, etc.)
  - Easy integration with existing RDKit workflows

### 2. Omega Replacement (Conformer Generation)

#### a. RDKit Conformer Generation
- **Type**: Open-source
- **Features**:
  - Multiple conformer generation methods
  - Energy minimization
  - Tight integration with other RDKit tools

#### b. Confab (from Open Babel)
- **Type**: Open-source
- **Features**:
  - Systematic conformer generation
  - Energy ranking
  - Command-line interface

## Implementation Strategy for Open-Source Pipeline

1. **Docking**:
   - Use AutoDock Vina or rDock as the primary docking engine
   - Implement proper PDBQT conversion using Open Babel

2. **Conformer Generation**:
   - Replace Omega with RDKit's conformer generation
   - Implement energy minimization and filtering

3. **Shape Similarity**:
   - Implement RDKit-based shape alignment
   - Add feature-based scoring using pharmacophore fingerprints

4. **Scoring Function**:
   - Combine docking scores with shape similarity
   - Add custom terms for PROTAC-specific features

## Configuration Example for Open-Source Backend

```json
{
  "docking": {
    "backend": "AutodockVina",
    "parameters": {
      "binary_location": "/path/to/vina",
      "receptor_pdbqt_path": "target.pdbqt",
      "search_space": {
        "center_x": 0.0,
        "center_y": 0.0,
        "center_z": 0.0,
        "size_x": 20.0,
        "size_y": 20.0,
        "size_z": 20.0
      }
    }
  },
  "scoring": {
    "components": [
      {
        "name": "docking_score",
        "weight": 1.0
      },
      {
        "name": "shape_similarity",
        "weight": 0.5,
        "parameters": {
          "reference_mol": "reference.sdf",
          "method": "rdkit_shape"
        }
      }
    ]
  }
}