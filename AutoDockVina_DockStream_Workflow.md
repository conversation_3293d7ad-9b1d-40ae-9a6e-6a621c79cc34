# AutoDock Vina Docking with Dock<PERSON>tream

## Overview
DockStream provides a streamlined interface to AutoDock Vina, a popular open-source molecular docking program. This document outlines the workflow and components involved in performing molecular docking using DockStream with AutoDock Vina.

## Core Components

1. **AutodockVinaExecutor**
   - Handles execution of the AutoDock Vina binary
   - Validates Vina installation and version
   - Manages command-line execution

2. **AutodockVina Docker**
   - Main class that orchestrates the docking process
   - Manages input/output file handling
   - Handles parallelization of docking jobs
   - Processes results and scores

3. **AutodockVinaParameters**
   - Configuration for the docking run
   - Includes search space definition (box dimensions)
   - Specifies number of poses, seed, and other Vina parameters

## Workflow

### 1. Input Preparation
- **Ligand Preparation**:
  - Input molecules can be provided in various formats (SMILES, SDF, etc.)
  - Multiple preparation backends supported:
    - RDKit
    - OpenEye
    - Ligprep
    - Corina
  - Molecules are converted to PDBQT format for docking

- **Receptor Preparation**:
  - Protein structure must be prepared in PDBQT format
  - Can include multiple receptor files for ensemble docking

### 2. Docking Configuration
Example configuration (JSON):
```json
{
  "backend": "AutoDockVina",
  "run_id": "docking_run_1",
  "input_pools": ["ligands"],
  "parameters": {
    "binary_location": "/path/to/vina/executable",
    "parallelization": {
      "number_cores": 4
    },
    "seed": 42,
    "receptor_pdbqt_path": [
      "/path/to/receptor.pdbqt"
    ],
    "number_poses": 3,
    "search_space": {
      "--center_x": 10.0,
      "--center_y": 10.0,
      "--center_z": 10.0,
      "--size_x": 20.0,
      "--size_y": 20.0,
      "--size_z": 20.0
    }
  }
}
```

### 3. Docking Execution
1. Input molecules are converted to PDBQT format if needed
2. Temporary working directories are created
3. Docking is performed in parallel across specified cores
4. For each ligand:
   - Input PDBQT is written to temporary file
   - AutoDock Vina is executed with specified parameters
   - Output poses are captured and processed

### 4. Result Processing
- Docking results are parsed and stored in memory
- Scores are extracted from Vina output
- Results can be written to various output formats
- Multiple output modes supported:
  - Best pose per ligand
  - Best pose per enumeration
  - All poses

## Key Features

1. **Parallel Processing**
   - Multi-core support for high-throughput docking
   - Efficient job distribution

2. **Flexible Input Handling**
   - Multiple input formats supported
   - Integration with various molecular preparation tools

3. **Result Management**
   - Comprehensive result storage
   - Easy access to scores and poses
   - Support for writing results in multiple formats

## Example Command Line
```bash
python -m dockstream.main -run <path_to_config.json>
```

## Output
- Docked poses in PDBQT format
- Docking scores and energy values
- Optional SDF output with embedded scores

## Best Practices
1. Always validate your input structures
2. Choose appropriate box dimensions to cover the binding site
3. Consider using multiple CPU cores for large datasets
4. Check the log files for any warnings or errors
5. Validate docking results with experimental data when available
