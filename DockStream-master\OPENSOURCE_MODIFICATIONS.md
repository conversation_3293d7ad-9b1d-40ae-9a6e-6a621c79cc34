# Open-Source Modifications for DockStream

This document outlines the changes made to make DockStream work with only open-source components, specifically for AutoDock Vina.

## Changes Made

### 1. Created New Parallelization Module
Created a new file `dockstream/utils/parallelization.py` to handle parallel processing without commercial dependencies.

### 2. Modified AutoDockVina Docker Module
Updated `dockstream/core/AutodockVina/AutodockVina_docker.py` to:
- Remove dependency on `Glide_docker.Parallelization`
- Use the new local `Parallelization` class
- Remove `MoleculeTranslator` import which had OpenEye dependencies

### 3. Updated Main Docker Script
Modified `docker.py` to:
- Comment out imports of commercial tools
- Add clear error messages about limited functionality
- Keep only AutoDock Vina as the supported backend

## How to Use

1. Create a configuration file (e.g., `vina_config.json`):

```json
{
  "docking": {
    "backend": "AutodockVina",
    "parameters": {
      "receptor_pdbqt_path": "path/to/receptor.pdbqt",
      "search_space": {
        "center_x": 0.0,
        "center_y": 0.0,
        "center_z": 0.0,
        "size_x": 20.0,
        "size_y": 20.0,
        "size_z": 20.0
      },
      "number_poses": 1,
      "binary_location": "vina"
    },
    "input": {
      "type": "smi",
      "path": "path/to/ligands.smi"
    },
    "output": {
      "pose_format": "sdf",
      "directory": "output"
    }
  }
}
```

2. Run the docking:

```bash
python docker.py -conf vina_config.json
```

## Notes

- This version only supports AutoDock Vina as the docking backend
- All commercial tool dependencies have been removed or commented out
- Some advanced features may be limited compared to the full version
- To restore commercial tool support, revert the changes and install the required commercial software

## Dependencies

- Python 3.7+
- RDKit
- OpenBabel
- AutoDock Vina
